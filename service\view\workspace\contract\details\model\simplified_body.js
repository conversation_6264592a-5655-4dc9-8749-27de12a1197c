import CustomContractBody from './custom_body';

export default class SimplifiedContractBody extends CustomContractBody {
  constructor(context, contract) {
    super(context, contract);
    this.contract_type = 'simplified_contract';
  }

  props() {
    const baseProps = super.props();
    
    return {
      base_settings: {
        ...baseProps.base_settings,
        group: "!base/simplified_contract_gen_data", 
      },
      base_data: {
        ...baseProps.base_data,
        group: "!base/simplified_contract_gen_data", 
        fields: {
          contract_status: baseProps.base_data.fields.contract_status,
          contract_name: baseProps.base_data.fields.contract_name,
          contract_close_at: baseProps.base_data.fields.contract_close_at,
          execution_date: baseProps.base_data.fields.execution_date,
          spec_totalcost_amount: baseProps.base_data.fields.spec_totalcost_amount,
          contract_type: baseProps.base_data.fields.contract_type,
          contract_reason: baseProps.base_data.fields.contract_reason,
          delivery_days: baseProps.base_data.fields.delivery_days,
          payment_days: baseProps.base_data.fields.payment_days,
          special_conditions: baseProps.base_data.fields.special_conditions,
        }
      },
      participants_settings: baseProps.participants_settings,
      participants: baseProps.participants,
      requisites: baseProps.requisites,
      actions: baseProps.actions,
      spec: baseProps.spec
    };
  }
}