import { Entity, DataSource, Query, Property } from '@iac/data';
import { Http, Language, Util } from '@iac/core';
import { Config, Context, Settings } from '@iac/kernel';

let SearchInput = {
  props: ["text"],
  data: function () {
    return {
      value: this.text,
      tid: undefined
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler(val, oldVal) {
        if (val == oldVal)
          return;

        if (val == '' || val == null)
          val = undefined

        if (this.tid) {
          clearTimeout(this.tid);
        }
        this.tid = setTimeout(async () => {
          this.$emit("search", val)
        }, 400);
      }
    }
  },
  template: `
        <input style='padding: 10px; border-radius: 5px;width: 100%; border: 1px solid #009ab8; outline: none;' :placeholder='$t("search")' v-model='value' />
    `
}

let validate = function () {
  if (this.value <= 0)
    return "Укажите значение больше 0"
}

let validate_advance = function () {
  if (this.value < 0)
    return "Укажите значение не менее 0"
}

class SimplifiedContractProduct extends Entity {
  constructor(context, contract) {
    super(context);

    this.is_gos = context.is_gos;
    this.claim_id = context.claim_id;
    this.product = context.product;
    this.start_price = context.start_price || context.price;
    this.start_currency = context.currency || Settings._default_currency;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;

    if (context.product?.unit) {
      this.amount = `${context.amount} ${context.product.unit}`;
    } else {
      this.amount = context.amount;
    }

    this.delivery_month = context.delivery_month;
    this.delivery_year = context.delivery_year;
    this.conditions = context.conditions;
    this.delivery_address = context.delivery_address;
    this.country = context.country;
  }

  props() {
    return {
      product: {
        type: "product",
        readonly: true,
        attr: { eye: true, short: true },
      },
      start_price: {
        group: "<start_price>",
        type: "float",
        label: "Стартовая цена",
        readonly: true,
        hidden: !this.is_gos,
        attr: { react: true },
      },
      start_currency: {
        group: "<start_price>",
        label: "currency",
        type: "entity",
        dataSource: 'ref_currency',
        hidden: !this.is_gos,
        readonly: true
      },
      price: {
        group: "<price>",
        type: "float",
        required: this.is_gos,
        hidden: !this.is_gos,
        attr: { react: true },
        validate: () => {
          if (this.price <= 0) {
            return "Укажите значение больше 0";
          }
          if (this.price > this.start_price) {
            return "Значение не может превышать стартовую цену";
          }
        }
      },
      currency: {
        group: "<price>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        hidden: !this.is_gos,
      },
      delivery_month: {
        group: '<delivery>',
        type: "entity",
        label: "contract.delivery_month",
        dataSource: DataSource.get("ref_months"),
        required: this.is_gos,
        hidden: !this.is_gos,
      },
      delivery_year: {
        group: "<delivery>",
        label: "contract.delivery_year",
        max: 2100,
        type: "number",
        required: this.is_gos,
        hidden: !this.is_gos,
      },
      country: {
        type: "entity",
        label: "country",
        dataSource: 'ref_country_',
        required: true,
      },
      conditions: {
        type: "text",
        label: "conditions_characteristics",
        required: true,
      },
      delivery_address: {
        type: "text",
        required: !this.is_gos,
        hidden: this.is_gos,
      },
    }
  }
}

class SimplifiedContractGraph extends Entity {
  constructor(context, contract) {
    super(context);
    this.source = context.source;
    this.summa = context.summa;
    this.advance_payment = context.advance_payment || 0;
    this.currency = context.currency || Settings._default_currency;
  }

  props() {
    return {
      source: {
        label: "comm_src",
        required: true,
      },
      summa: {
        group: "<summa>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: validate
      },
      currency: {
        group: "<summa>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        required: true,
      },
      advance_payment: {
        type: "float",
        label: "advance_payment_amount",
        required: false,
        attr: { react: true },
        validate: validate_advance
      },
    }
  }
}

class SimplifiedContractGraphFinance extends Entity {
  constructor(context, contract) {
    super(context);
    this.date = context.date;
    this.summa = context.summa;
    this.advance_payment = context.advance_payment || 0;
    this.currency = context.currency || Settings._default_currency;
    this.kls = context.kls;
    this.expense_item_code = context.expense_item_code;
    this.is_gos = context.is_gos;
    this.claim_accounts = context.claim_accounts || [];
  }

  props() {
    return {
      date: {
        type: "model",
        label: "!",
        fields: {
          month: {
            type: "entity",
            dataSource: "ref_months",
            group: "<date>",
            required: true,
          },
          year: {
            type: "number",
            group: "<date>",
            max: 2100,
            required: true,
          }
        }
      },
      kls: {
        label: "bank_account",
        type: "entity",
        required: this.is_gos,
        hidden: !this.is_gos,
        dataSource: this.claim_accounts,
      },
      expense_item_code: {
        group: '!type-',
        type: "entity",
        dataSource: "ref_expense_item",
        required: this.is_gos,
        hidden: !this.is_gos
      },
      summa: {
        group: "<summa>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: validate
      },
      currency: {
        group: "<summa>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        required: true,
      },
      advance_payment: {
        type: "float",
        label: "advance_payment_amount",
        required: false,
        attr: { react: true },
        validate: validate_advance
      },
    }
  }
}

class SimplifiedContractBodyProperty extends Property {
  constructor(context) {
    super(context);
    this._meta = context.meta || {};
  }

  get meta() {
    return this._meta
  }
}

export default class SimplifiedContractBody extends Entity {
  constructor(context, contract) {
    super(context);
    this.context = context;
    this.contract = contract;
    this.wait = false;
    this.claim_data = context.claim_data || [];
    this.claim_accounts = this.claim_data.map((item) => { return { id: item.kls, name: item.kls }; });
    this.claim_data = this.claim_data.reduce((acc, item) => {
      acc[item.id] = { account: item.kls, expense_item_code: item.expense };
      return acc;
    }, {});
    this.additional_contract = context.additional_contract;
    this.base_settings = context.base_settings || {};
    this.base_data = context.base_data || {};
    this.participants_settings = context.participants_settings || {};
    this.participants = context.participants || {};
    this.requisites = context.requisites || {};
    this.actions = context.actions || {};
    this._graph = context.graph || []
    this._graph_finance = context.graph_finance || []
    this._spec = context.spec || []
  }

  get propertyModel() {
    return SimplifiedContractBodyProperty;
  }

  async edit_product(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractProduct({ is_gos: this.contract.is_gos, ...item })).fields
    });
  }

  async edit_graph(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractGraph({ currency: this.currency, ...item })).fields
    })
  }

  async edit_graph_finance(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractGraphFinance({ currency: this.currency, is_gos: this.contract.is_gos, claim_accounts: this.claim_accounts, ...item })).fields
    })
  }

  async update_graph() {
    let { error, data } = await this.contract.action('get_graph', {});

    if (!error && data) {
      this.properties.graph.dataSource.set_items(data);
    }
  }

  async onChangeProperty(field) {
    if (!field || field.property.type != 'file')
      return;

    var send = async (file) => {
      let formData = new FormData();
      formData.append('data', file, file.name);
      let { data, error } = await Http.upload.form('tender/attach', formData);
      if (error) {
        return;
      }

      return {
        id: data.uuid,
        name: data.meta.name,
        meta: {
          "type": data.meta.type,
          "content_type": data.meta.content_type,
          "type_group": data.meta.group,
          "size": data.meta.size
        }
      }
    }

    let value = field.value;
    this.properties.setting_general.wait = true;
    if (Array.isArray(value)) {
      for (let key in value) {
        let item = value[key];
        if (item && item.file && item.file.name) {
          let _value = await send(item.file);
          value[key] = _value;
        }
      }
      field.property._value = [...value]
    } else {
      let item = value;
      if (item && item.file && item.file.name) {
        let _value = await send(item.file);
        value = field.property._value = _value
      }
    }
    this.properties.setting_general.wait = false;
  }

  validate_fields(fields) {
    let errors = [];
    for (let field of fields) {
      if (field.validate && typeof field.validate === 'function') {
        let error = field.validate();
        if (error) {
          errors.push(error);
        }
      }
    }
    return errors.length === 0;
  }

  build_action_params(fields) {
    return fields.filter((field) => {
      if (field.type == 'static') return false;
      if (field.hidden && typeof field.hidden == 'function') return !field.hidden();
      return !field.hidden;
    }).map((field) => {
      let value = field.value;
      if (value && value.exp && value.exp.value != undefined) value = value.exp.value
      if (Array.isArray(value) && value.length <= 0) value = undefined;
      if (value && !Array.isArray(value) && typeof value == 'object') value = { ...value }
      return { name: field.name, value: value }
    }).filter((field) => {
      return field.value != undefined
    }).reduce((acc, field) => {
      acc[field.name] = field.value;
      return acc;
    }, {});
  }

  props() {
    return {
      base_settings: {
        group: "!base/simplified_contract_gen_data",
        type: "setting",
        attr: {
          style: "max-width: 800px;"
        }
      },
      base_data: {
        type: "model",
        label: "!",
        group: "!base/simplified_contract_gen_data",
        readonly: !this.contract.rights?.set_contract_data,
        fields: {
          contract_status: {
            label: '-status',
            type: "static",
            readonly: true,
          },
          version_number: {
            label: '-contract.version_number',
            type: "static",
            readonly: true,
            hidden: () => {
              return !this.version_number
            }
          },
          lot_id: {
            type: "static",
            label: '-contract.lot_id',
            readonly: true,
          },
          ckepik_id: {
            type: "number",
            label: '-contract.ckepik_id',
            readonly: !this.contract.rights?.set_contragent_data,
            required: false,
          },
          contract_name: {
            label: '-contract.contract_name',
            required: true,
            readonly: !this.contract.rights?.set_contragent_data,
          },
          hide_in_public: {
            label: '-contract.hide_in_public',
            type: "bool",
            readonly: this.additional_contract,
          },
          contract_close_at: {
            label: "-contract.contract_close_at",
            type: "date",
            required: true,
            validate: () => {
              let value = this.base_data.contract_close_at;
              if (!value || !this.base_data.execution_date) { return }
              if (value < this.base_data.execution_date) {
                return Language.t("contract.contract_close_at_error");
              }
            }
          },
          execution_date: {
            label: "-contract.execution_date",
            type: "date",
            required: true,
            validate: () => {
              let value = this.base_data.execution_date;
              if (!value || !this.base_data.contract_close_at) { return }
              if (value > this.base_data.contract_close_at) {
                return Language.t("contract.execution_date_error");
              }
            }
          },
          spec_totalcost_amount: {
            label: "-contract.spec_totalcost_amount",
            type: "static",
            readonly: true,
          },
          contract_type: {
            label: "-contract.contract_type",
            type: "entity",
            dataSource: "ref_contract_type_",
            required: true,
            readonly: !this.contract.rights?.set_contract_data,
          },
          addons: {
            label: "-!",
            type: "enum",
            hidden: () => {
              return !this.contract_type || this.contract_type.id != 4
            },
            dataSource: [
              { id: "1", name: Language.t("direct_contract_type1") },
              { id: "2", name: Language.t("direct_contract_type2") },
              { id: "3", name: Language.t("direct_contract_type3") },
              { id: "4", name: Language.t("direct_contract_type4") },
            ],
          },
          beneficiary: {
            type: "model",
            label: "-dir_contract_beneficiary",
            fields: {
              title: {
                label: "!title",
                group: "<beneficiary>"
              },
              inn: {
                label: "!inn",
                group: "<beneficiary>",
                type: "number"
              }
            },
          },
          delivery_days: {
            label: "-contract.delivery_days",
            type: "integer",
            required: true,
            validate: validate,
          },
          payment_days: {
            label: "-contract.payment_days",
            type: "integer",
            required: true,
            validate: validate,
          },
          contract_reason: {
            label: "-contract.contract_reason",
            type: "entity",
            required: true,
            readonly: !this.contract.rights?.set_contract_data,
            dataSource: {
              limit: 99,
              store: {
                method: "ref",
                ref: "ref_contract_reasons",
                injectQuery: (params) => {
                  params.filters = params.filters || {};
                  params.filters.proc_type = "simplified_contract";
                  return params;
                }
              }
            },
          },
          special_conditions: {
            label: "-contract.special_conditions",
            type: "text",
          },
        }
      },
      participants_settings: {
        group: "!participants/participants_settings",
        type: "setting",
        attr: {
          style: "max-width: 800px;"
        }
      },
      participants: {
        type: "model",
        label: "!",
        group: "!participants/participants_settings",
        readonly: !this.contract.rights?.set_contragent_data,
        fields: {
          contragent: {
            label: "-contract.contragent",
            type: "entity",
            required: true,
            readonly: !this.contract.rights?.set_contragent_data,
            dataSource: {
              limit: 99,
              store: {
                method: "ref",
                ref: "ref_contragent_",
                injectQuery: (params) => {
                  params.filters = params.filters || {};
                  if (this.contract.is_gos) {
                    params.filters.is_gos = true;
                  }
                  return params;
                }
              }
            },
          },
          custom_company: {
            type: "model",
            label: "!",
            hidden: () => {
              return (
                !this.base_data.contract_type
                || this.base_data.contract_type == 1
                || !this.participants.contragent?.custom_company
              )
            },
            fields: {
              company_name: {
                required: true,
                label: "-direct_contract_comp_name"
              },
              activity_area_id: {
                label: "-activity_area_id",
                type: "entity",
                dataSource: "ref_area_",
                required: true,
              },
              legal_address: {
                label: "-legal_address",
                required: true,
              },
              phone: {
                label: "-phone",
                required: true,
              },
              fax: {
                label: "-fax"
              },
              inn: {
                label: "-inn",
                required: true,
              },
              oked_code: {
                label: "-oked_code",
                type: 'entity',
                required: true,
                dataSource: "ref_economic_acivity_type_"
              },
            }
          },
        }
      },
      requisites: {
        type: "model",
        label: "!",
        group: "!requisites/requisites",
        readonly: !this.contract.rights?.set_requisites,
        fields: {
          org_banking_details: {
            group: "!company_bank/!requisites-/contract.requisites_org",
            type: 'entity',
            label: "requisites_title",
            dataSource: "ref_org_banking_details_",
            required: true,
            readonly: !this.contract.rights?.set_requisites,
          },
          contragent_banking_details: {
            group: "!company_bank/!requisites-/contract.requisites_contragent",
            type: 'entity',
            label: "requisites_title",
            dataSource: "ref_contragent_banking_details_",
            required: true,
            readonly: !this.contract.rights?.set_requisites,
          }
        }
      },
      actions: {
        type: "list",
        label: "!",
        group: "!actions/actions",
        readonly: !this.contract.rights?.set_actions,
        attr: {
          action_name: !this.contract.is_gos && Language.t("actions"),
          action_style: !this.contract.is_gos && "min-width: 110px;text-align:left;",
          buttons: true,
          summary: true,
          not_found: "",
          columns: [
            {
              field: "source", label: "product_name", style: "width: 100%; text-align: left;",
              summary: (items) => {
                if (!items)
                  return;
                let count = items.filter((item) => {
                  return !((item.status & 4) != 0);
                }).length;
                return `${count} ${Language.t("source", { count: count })}`
              }
            },
            {
              field: "summa", label: "summa", style: "width: 150px; text-align: right;",
              summary: (items) => {
                if (!items)
                  return;
                let summa = items.filter((item) => {
                  return !((item.status & 4) != 0);
                }).reduce((acc, item) => {
                  return acc + (item.summa || 0);
                }, 0);
                return Util.formatNumber(summa, 2);
              }
            },
            { field: "currency", label: "currency", style: "width: 80px; text-align: center;" },
          ],
          actions: [
            {
              label: "edit",
              action: async (item) => {
                let result = await this.edit_graph(item);
                if (result) {
                  Object.assign(item, result);
                  await this.update_graph();
                }
              }
            },
            {
              label: "delete",
              action: async (item) => {
                if (await Vue.Dialog.MessageBox.Question(Language.t('delete_question')) == Vue.Dialog.MessageBox.Result.Yes) {
                  item.status = item.status | 4;
                  await this.update_graph();
                }
              }
            }
          ]
        },
        dataSource: DataSource.get(this._graph, SimplifiedContractGraph),
        onAdd: async () => {
          let result = await this.edit_graph();
          if (result) {
            this.properties.actions.dataSource.add(result);
            await this.update_graph();
          }
        }
      },
      spec: {
        type: "list",
        label: "!",
        group: "!spec/spec",
        readonly: !this.contract.rights?.set_spec,
        attr: {
          action_name: Language.t("actions"),
          action_style: "min-width: 110px;text-align:left;",
          buttons: true,
          summary: true,
          not_found: "",
          columns: [
            {
              field: "product", label: "product_name", style: "width: 100%; text-align: left;",
              summary: (items) => {
                if (!items)
                  return;
                let count = items.filter((item) => {
                  return !((item.status & 4) != 0);
                }).length;
                return `${count} ${Language.t("product", { count: count })}`
              }
            },
            { field: "amount", label: "amount", style: "width: 150px; text-align: right;" },
            { field: "price", label: "price", style: "width: 150px; text-align: right;" },
            { field: "currency", label: "currency", style: "width: 80px; text-align: center;" },
          ],
          actions: [
            {
              label: "edit",
              action: async (item) => {
                let result = await this.edit_product(item);
                if (result) {
                  Object.assign(item, result);
                }
              }
            },
            {
              label: "delete",
              action: async (item) => {
                if (await Vue.Dialog.MessageBox.Question(Language.t('delete_question')) == Vue.Dialog.MessageBox.Result.Yes) {
                  item.status = item.status | 4;
                }
              }
            }
          ]
        },
        dataSource: DataSource.get(this._spec, SimplifiedContractProduct),
        onAdd: async () => {
          let result = await this.edit_product();
          if (result) {
            this.properties.spec.dataSource.add(result);
          }
        }
      }
    };
  }
}
